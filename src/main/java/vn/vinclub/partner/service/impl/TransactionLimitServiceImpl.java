package vn.vinclub.partner.service.impl;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.redisson.api.RBucket;
import org.redisson.api.RedissonClient;
import org.redisson.codec.JsonJacksonCodec;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.stereotype.Service;
import vn.vinclub.common.annotation.Profiler;
import vn.vinclub.partner.constant.AppErrorCode;
import vn.vinclub.partner.domain.dto.UserQuotaDto;
import vn.vinclub.partner.domain.dto.point.PartnerPointTxnFilterDto;
import vn.vinclub.partner.domain.entity.PartnerPointTransactionHistory;
import vn.vinclub.partner.domain.enums.ActorSystem;
import vn.vinclub.partner.domain.enums.PointHistoryStatus;
import vn.vinclub.partner.domain.enums.TransactionType;
import vn.vinclub.partner.exception.BusinessLogicException;
import vn.vinclub.partner.service.PartnerPointConfigService;
import vn.vinclub.partner.service.PartnerPointTransactionHistoryService;
import vn.vinclub.partner.service.TransactionLimitService;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.time.*;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.stream.Collectors;

@Service
@Slf4j
@RequiredArgsConstructor
public class TransactionLimitServiceImpl implements TransactionLimitService {

    private static final String DAILY_USER_QUOTA_CACHE_KEY = "partner_svc_daily_user_quota:%s:%s";
    private static final String MONTHLY_USER_QUOTA_CACHE_KEY = "partner_svc_monthly_user_quota:%s:%s";

    private static final Set<PointHistoryStatus> COUNTED_STATUSES = Set.of(PointHistoryStatus.PROCESSING, PointHistoryStatus.SUCCESS);

    private final PartnerPointConfigService partnerPointConfigService;
    private final PartnerPointTransactionHistoryService partnerPointTransactionHistoryService;

    private final RedissonClient redissonClient;

    @Value("${default.vinclub.point.code}")
    private String defaultVinclubPointCode;

    @Value("${default.vinclub.point.cash.value}")
    private BigDecimal defaultVinclubPointCashValue;

    @Value("${partner.transaction.max-request-by-user-per-day}")
    private Long maxRequestByUserPerDay;

    @Value("${partner.transaction.max-cash-amount-by-user-per-day}")
    private Long maxCashAmountByUserPerDay;

    @Profiler
    @Override
    public void validateLimits(Long partnerId, String partnerUserId, Long pointAmount, String pointCode, TransactionType transactionType, ActorSystem actorSystem) {
        UserQuotaDto userQuota = getDailyUserQuota(partnerId, partnerUserId);

        if (userQuota.getTotalTransaction() >= maxRequestByUserPerDay) {
            throw new BusinessLogicException(AppErrorCode.DAILY_TRANSACTION_COUNT_LIMIT_EXCEEDED);
        }

        BigDecimal cashValue = calculateCashValue(pointAmount, pointCode, partnerId);
        if (userQuota.getTotalCashAmount().add(cashValue).compareTo(BigDecimal.valueOf(maxCashAmountByUserPerDay)) > 0) {
            throw new BusinessLogicException(AppErrorCode.DAILY_CASH_AMOUNT_LIMIT_EXCEEDED);
        }

        // validate by partner config
        validateByPartnerConfig(partnerId, partnerUserId, pointAmount, pointCode, transactionType, actorSystem);
    }

    @Profiler
    @Override
    public void updateQuota(PartnerPointTransactionHistory oldTransaction, PartnerPointTransactionHistory newTransaction) {
        // Handle transaction creation
        if (oldTransaction == null && newTransaction != null) {
            if (COUNTED_STATUSES.contains(newTransaction.getStatus())) {
                incrementQuota(newTransaction);
            }
            return;
        }

        // Handle transaction deletion
        if (oldTransaction != null && newTransaction == null) {
            if (COUNTED_STATUSES.contains(oldTransaction.getStatus())) {
                decrementQuota(oldTransaction);
            }
            return;
        }

        // Handle transaction update
        if (newTransaction != null) {
            boolean oldCounted = COUNTED_STATUSES.contains(oldTransaction.getStatus());
            boolean newCounted = COUNTED_STATUSES.contains(newTransaction.getStatus());

            if (!oldCounted && newCounted) {
                // Transaction moved to counted status
                incrementQuota(newTransaction);
            } else if (oldCounted && !newCounted) {
                // Transaction moved from counted status
                decrementQuota(oldTransaction);
            }
        }
    }

    @Profiler
    private void validateByPartnerConfig(Long partnerId, String partnerUserId, Long pointAmount, String pointCode, TransactionType transactionType, ActorSystem actorSystem) {
    }

    @Profiler
    private UserQuotaDto getDailyUserQuota(Long partnerId, String partnerUserId) {
        String cacheKey = String.format(DAILY_USER_QUOTA_CACHE_KEY, partnerId, partnerUserId);
        RBucket<UserQuotaDto> bucket = redissonClient.getBucket(cacheKey, new JsonJacksonCodec());

        if (bucket.isExists()) {
            return bucket.get();
        }

        Long startOfDay = LocalDate.now().atStartOfDay(ZoneId.systemDefault()).toInstant().toEpochMilli();
        Long endOfDay = LocalDate.now().atTime(LocalTime.MAX).atZone(ZoneId.systemDefault()).toInstant().toEpochMilli();

        UserQuotaDto quota = getUserQuotaDto(partnerId, partnerUserId, startOfDay, endOfDay);
        bucket.set(quota, Duration.ofMillis(getRemainingTimeToEndOfDay()));

        return quota;
    }

    @Profiler
    private UserQuotaDto getMonthlyUserQuota(Long partnerId, String partnerUserId) {
        String cacheKey = String.format(MONTHLY_USER_QUOTA_CACHE_KEY, partnerId, partnerUserId);
        RBucket<UserQuotaDto> bucket = redissonClient.getBucket(cacheKey, new JsonJacksonCodec());

        if (bucket.isExists()) {
            return bucket.get();
        }

        Long startOfMonth = LocalDate.now().withDayOfMonth(1).atStartOfDay(ZoneId.systemDefault()).toInstant().toEpochMilli();
        Long endOfMonth = LocalDate.now().withDayOfMonth(LocalDate.now().lengthOfMonth()).atTime(LocalTime.MAX).atZone(ZoneId.systemDefault()).toInstant().toEpochMilli();

        UserQuotaDto quota = getUserQuotaDto(partnerId, partnerUserId, startOfMonth, endOfMonth);
        bucket.set(quota, Duration.ofMillis(getRemainingTimeToEndOfDay()));

        return quota;
    }

    @Profiler
    private UserQuotaDto getUserQuotaDto(Long partnerId, String partnerUserId, Long fromTime, Long endTime) {
        Page<PartnerPointTransactionHistory> transactions = partnerPointTransactionHistoryService.filter(
                PartnerPointTxnFilterDto.builder()
                        .partnerId(partnerId)
                        .partnerUserId(partnerUserId)
                        .requestTimeFrom(fromTime)
                        .requestTimeTo(endTime)
                        .build(),
                Pageable.unpaged()
        );
        return calculateUserQuota(transactions.getContent());
    }

    @Profiler
    private UserQuotaDto calculateUserQuota(List<PartnerPointTransactionHistory> transactions) {
        if (transactions.isEmpty()) {
            return UserQuotaDto.builder()
                    .totalTransaction(0L)
                    .totalCashAmount(BigDecimal.ZERO)
                    .build();
        }

        Map<TransactionType, Map<String, Long>> totalPartnerPointTransactionByTypeAndPointCode = transactions.stream()
                .filter(transaction -> ActorSystem.VINCLUB.equals(transaction.getActorSystem())) // Vinclub -> Partner
                .collect(Collectors.groupingBy(PartnerPointTransactionHistory::getTransactionType,
                        Collectors.groupingBy(PartnerPointTransactionHistory::getPointCode, Collectors.counting())));

        Map<TransactionType, Map<String, Long>> totalPartnerPointAmountByTypeAndPointCode = transactions.stream()
                .filter(transaction -> ActorSystem.VINCLUB.equals(transaction.getActorSystem())) // Vinclub -> Partner
                .collect(Collectors.groupingBy(PartnerPointTransactionHistory::getTransactionType,
                        Collectors.groupingBy(PartnerPointTransactionHistory::getPointCode,
                                Collectors.reducing(0L, PartnerPointTransactionHistory::getPointAmount, Long::sum))));

        Map<TransactionType, Long> totalVinclubPointTransactionByType = transactions.stream()
                .filter(transaction -> ActorSystem.PARTNER.equals(transaction.getActorSystem())) // Partner -> Vinclub
                .collect(Collectors.groupingBy(PartnerPointTransactionHistory::getTransactionType, Collectors.counting()));

        Map<TransactionType, Long> totalVinclubPointAmountByType = transactions.stream()
                .filter(transaction -> ActorSystem.PARTNER.equals(transaction.getActorSystem())) // Partner -> Vinclub
                .collect(Collectors.groupingBy(PartnerPointTransactionHistory::getTransactionType,
                        Collectors.reducing(0L, PartnerPointTransactionHistory::getPointAmount, Long::sum)));


        BigDecimal totalCashAmount = transactions.stream()
                .map(transaction -> calculateCashValueForTransaction(transaction, transaction.getPartnerId()))
                .reduce(BigDecimal.ZERO, BigDecimal::add);

        return UserQuotaDto.builder()
                .totalTransaction((long) transactions.size())
                .totalCashAmount(totalCashAmount)
                .totalPartnerPointTransactionByTypeAndPointCode(totalPartnerPointTransactionByTypeAndPointCode)
                .totalPartnerPointAmountByTypeAndPointCode(totalPartnerPointAmountByTypeAndPointCode)
                .totalVinclubPointTransactionByType(totalVinclubPointTransactionByType)
                .totalVinclubPointAmountByType(totalVinclubPointAmountByType)
                .build();
    }

    @Profiler
    private void decrementQuota(PartnerPointTransactionHistory transaction) {
    }

    @Profiler
    private void incrementQuota(PartnerPointTransactionHistory transaction) {
    }

    @Profiler
    private BigDecimal calculateCashValueForTransaction(PartnerPointTransactionHistory transaction, Long partnerId) {
        Long pointAmount = transaction.getPointAmount();
        String pointCode = transaction.getPointCode();

        if (pointAmount == null || pointAmount <= 0) {
            return BigDecimal.ZERO;
        }

        BigDecimal cashValuePerPoint = getCashValuePerPoint(pointCode, partnerId);
        return cashValuePerPoint.multiply(BigDecimal.valueOf(pointAmount));
    }

    @Profiler
    private BigDecimal calculateCashValue(Long pointAmount, String pointCode, Long partnerId) {
        if (pointAmount == null || pointAmount <= 0) {
            return BigDecimal.ZERO;
        }

        BigDecimal cashValuePerPoint = getCashValuePerPoint(pointCode, partnerId);
        return cashValuePerPoint.multiply(BigDecimal.valueOf(pointAmount));
    }

    @Profiler
    private BigDecimal getCashValuePerPoint(String pointCode, Long partnerId) {
        // Default to VinClub point cash value
        if (defaultVinclubPointCode.equals(pointCode)) {
            return defaultVinclubPointCashValue;
        }

        // For other point codes, get from partner point config
        try {
            var pointConfig = partnerPointConfigService.findByCode(partnerId, pointCode);
            if (pointConfig.getExchangeCashRate() != null) {
                BigDecimal exchangeValue = pointConfig.getExchangeCashRate().getExchangeValue();
                Long pointValue = pointConfig.getExchangeCashRate().getPointValue();

                if (exchangeValue != null && pointValue != null && pointValue > 0) {
                    return exchangeValue.divide(BigDecimal.valueOf(pointValue), RoundingMode.HALF_UP);
                }
            }
        } catch (Exception e) {
            log.warn("Failed to get cash value for pointCode={}, partnerId={}, using 0", pointCode, partnerId, e);
        }

        return BigDecimal.ZERO;
    }

    private Long getRemainingTimeToEndOfDay() {
        return LocalDate.now().atTime(LocalTime.MAX).atZone(ZoneId.systemDefault()).toInstant().toEpochMilli() - System.currentTimeMillis();
    }
}
