package vn.vinclub.partner.service.impl;

import com.fasterxml.jackson.databind.node.JsonNodeFactory;
import com.fasterxml.jackson.databind.node.ObjectNode;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.SerializationUtils;
import org.redisson.api.RMapCache;
import org.redisson.api.RedissonClient;
import org.redisson.codec.SerializationCodec;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.StringUtils;
import vn.vinclub.common.annotation.Profiler;
import vn.vinclub.common.util.BaseJsonUtils;
import vn.vinclub.partner.constant.AppErrorCode;
import vn.vinclub.partner.constant.MetadataKey;
import vn.vinclub.partner.domain.dto.point.PartnerPointTxnFilterDto;
import vn.vinclub.partner.domain.entity.PartnerPointTransactionHistory;
import vn.vinclub.partner.domain.enums.ActorSystem;
import vn.vinclub.partner.domain.enums.HistoricalAction;
import vn.vinclub.partner.domain.enums.PointHistoryStatus;
import vn.vinclub.partner.domain.enums.TransactionType;
import vn.vinclub.partner.domain.mapper.PartnerPointTransactionHistoryMapper;
import vn.vinclub.partner.domain.specification.PartnerPointTransactionHistorySpecification;
import vn.vinclub.partner.exception.BusinessLogicException;
import vn.vinclub.partner.repository.PartnerPointTransactionHistoryRepository;
import vn.vinclub.partner.service.DistributedIdGenerator;
import vn.vinclub.partner.service.LogHistoricalService;
import vn.vinclub.partner.service.PartnerPointTransactionHistoryService;

import java.time.Instant;
import java.util.List;
import java.util.Objects;
import java.util.Optional;
import java.util.concurrent.TimeUnit;

@Service
@RequiredArgsConstructor
@Slf4j
public class PartnerPointTransactionHistoryServiceImpl implements PartnerPointTransactionHistoryService {
    private static final String CACHE_BY_TRANSACTION_ID_KEY = "partner_svc_point_txn:by_transaction_id";
    private static final int CACHE_TTL_MINUTES = 5;

    private final PartnerPointTransactionHistoryRepository partnerPointTransactionHistoryRepository;
    private final PartnerPointTransactionHistoryMapper partnerPointTransactionHistoryMapper;

    private final LogHistoricalService logHistoricalService;
    private final DistributedIdGenerator pointTxnIdGenerator;
    private final RedissonClient redissonClient;
    private final BaseJsonUtils jsonUtils;

    @Value("${default.vinclub.point.code}")
    private String defaultVinclubPointCode;


    @Override
    @Transactional
    @Profiler
    public PartnerPointTransactionHistory initTopUpPointTxnFromPartner(
            Long partnerId,
            String partnerUserId,
            Long vclubUserId,
            String partnerTransactionId,
            Long pointAmount,
            String description,
            ObjectNode metadata) {

        // Check for an existing transaction with the same partner transaction ID (idempotency)
        Optional<PartnerPointTransactionHistory> existingTxn =
                partnerPointTransactionHistoryRepository.findByPartnerIdAndPartnerTransactionId(
                        partnerId, partnerTransactionId).filter(PartnerPointTransactionHistory::getActive);

        if (existingTxn.isPresent()) {
            log.error("Transaction already exists with partnerTransactionId: {}", partnerTransactionId);
            throw new BusinessLogicException(
                    partnerPointTransactionHistoryMapper.toExternalResponseDto(existingTxn.get()),
                    AppErrorCode.TRANSACTION_ID_PROCESSED
            );
        }

        PartnerPointTransactionHistory txn = partnerPointTransactionHistoryMapper.createTransaction(
                partnerId,
                partnerUserId,
                vclubUserId,
                ActorSystem.PARTNER,
                TransactionType.TOP_UP_POINT,
                generateTransactionId(),
                partnerTransactionId,
                description,
                pointAmount,
                defaultVinclubPointCode,
                null,
                metadata
        );

        // Save the updated transaction
        PartnerPointTransactionHistory savedTxn = partnerPointTransactionHistoryRepository.save(txn);

        // Trigger historical event
        logHistoricalService.logHistoricalEvent(null, savedTxn, savedTxn.getUpdatedTime(), HistoricalAction.CREATED);

        return savedTxn;
    }

    @Override
    @Transactional
    @Profiler
    public PartnerPointTransactionHistory initSpendPointTxnFromPartner(
            Long partnerId,
            String partnerUserId,
            Long vclubUserId,
            String partnerTransactionId,
            Long pointAmount,
            String description,
            ObjectNode metadata) {

        // Check for existing transaction with the same partner transaction ID (idempotency)
        Optional<PartnerPointTransactionHistory> existingTxn =
                partnerPointTransactionHistoryRepository.findByPartnerIdAndPartnerTransactionId(
                        partnerId, partnerTransactionId).filter(PartnerPointTransactionHistory::getActive);

        if (existingTxn.isPresent()) {
            log.error("Transaction already exists with partnerTransactionId: {}", partnerTransactionId);
            throw new BusinessLogicException(
                    partnerPointTransactionHistoryMapper.toExternalResponseDto(existingTxn.get()),
                    AppErrorCode.TRANSACTION_ID_PROCESSED
            );
        }

        PartnerPointTransactionHistory txn = partnerPointTransactionHistoryMapper.createTransaction(
                partnerId,
                partnerUserId,
                vclubUserId,
                ActorSystem.PARTNER,
                TransactionType.SPEND_POINT,
                generateTransactionId(),
                partnerTransactionId,
                description,
                pointAmount,
                defaultVinclubPointCode,
                null,
                metadata
        );

        // Save the updated transaction
        PartnerPointTransactionHistory savedTxn = partnerPointTransactionHistoryRepository.save(txn);

        // Trigger historical event
        logHistoricalService.logHistoricalEvent(null, savedTxn, savedTxn.getUpdatedTime(), HistoricalAction.CREATED);

        return savedTxn;
    }

    @Override
    @Transactional
    @Profiler
    public PartnerPointTransactionHistory initTopUpPointTxnToPartner(
            Long partnerId,
            String partnerUserId,
            Long vclubUserId,
            Long pointAmount,
            String pointCode,
            String description,
            ObjectNode metadata,
            String internalRefCode) {

        // Check for existing transaction with the processed balance history ID (idempotency)
        Optional<PartnerPointTransactionHistory> existingTxn =
                partnerPointTransactionHistoryRepository.findByInternalRefCode(internalRefCode)
                        .filter(PartnerPointTransactionHistory::getActive);

        if (existingTxn.isPresent()) {
            log.error("Transaction already exists with internalRefCode: {}", internalRefCode);
            throw new BusinessLogicException(AppErrorCode.TRANSACTION_ID_PROCESSED);
        }

        PartnerPointTransactionHistory txn = partnerPointTransactionHistoryMapper.createTransaction(
                partnerId,
                partnerUserId,
                vclubUserId,
                ActorSystem.VINCLUB,
                TransactionType.TOP_UP_POINT,
                generateTransactionId(),
                null,
                description,
                pointAmount,
                pointCode,
                internalRefCode,
                metadata
        );

        txn = partnerPointTransactionHistoryRepository.save(txn);

        // Trigger historical event
        logHistoricalService.logHistoricalEvent(null, txn, txn.getUpdatedTime(), HistoricalAction.CREATED);

        return txn;
    }

    @Override
    @Transactional
    @Profiler
    public PartnerPointTransactionHistory initSpendPointTxnToPartner(
            Long partnerId,
            String partnerUserId,
            Long vclubUserId,
            Long pointAmount,
            String pointCode,
            String description,
            ObjectNode metadata,
            String internalRefCode) {

        // Check for existing transaction with the processed balance history ID (idempotency)
        Optional<PartnerPointTransactionHistory> existingTxn =
                partnerPointTransactionHistoryRepository.findByInternalRefCode(internalRefCode)
                        .filter(PartnerPointTransactionHistory::getActive);

        if (existingTxn.isPresent()) {
            log.error("Transaction already exists with internalRefCode: {}", internalRefCode);
            throw new BusinessLogicException(AppErrorCode.TRANSACTION_ID_PROCESSED);
        }

        PartnerPointTransactionHistory txn = partnerPointTransactionHistoryMapper.createTransaction(
                partnerId,
                partnerUserId,
                vclubUserId,
                ActorSystem.VINCLUB,
                TransactionType.SPEND_POINT,
                generateTransactionId(),
                null,
                description,
                pointAmount,
                pointCode,
                internalRefCode,
                metadata
        );

        // Save the updated transaction
        PartnerPointTransactionHistory savedTxn = partnerPointTransactionHistoryRepository.save(txn);

        // Trigger historical event
        logHistoricalService.logHistoricalEvent(null, savedTxn, savedTxn.getUpdatedTime(), HistoricalAction.CREATED);

        return savedTxn;
    }

    @Override
    @Transactional
    @Profiler
    public PartnerPointTransactionHistory markTxnFromPartnerSuccess(String transactionId, String internalRefCode) {
        PartnerPointTransactionHistory txn = findAndValidateTransaction(transactionId);

        if (txn.getStatus() != PointHistoryStatus.PROCESSING) {
            throw new BusinessLogicException(AppErrorCode.INVALID_TRANSACTION_STATUS);
        }

        if (txn.getActorSystem() != ActorSystem.PARTNER) {
            throw new BusinessLogicException(AppErrorCode.INVALID_TRANSACTION_ACTOR_SYSTEM);
        }

        // Store the old transaction state for historical event
        PartnerPointTransactionHistory oldTxn = SerializationUtils.clone(txn);

        // Update the transaction
        txn.setStatus(PointHistoryStatus.SUCCESS);
        txn.setProcessedTime(Instant.now().toEpochMilli());
        txn.setInternalRefCode(internalRefCode);

        // Save the updated transaction
        PartnerPointTransactionHistory savedTxn = partnerPointTransactionHistoryRepository.save(txn);

        // Trigger historical event
        logHistoricalService.logHistoricalEvent(oldTxn, savedTxn, savedTxn.getUpdatedTime(), HistoricalAction.UPDATED);

        return savedTxn;
    }

    @Override
    @Transactional
    @Profiler
    public PartnerPointTransactionHistory makeTxnToPartnerSuccess(String transactionId, String partnerTransactionId, String request, String response, Long processedTime) {
        PartnerPointTransactionHistory txn = findAndValidateTransaction(transactionId);

        if (txn.getStatus() != PointHistoryStatus.PROCESSING) {
            throw new BusinessLogicException(AppErrorCode.INVALID_TRANSACTION_STATUS);
        }

        if (txn.getActorSystem() != ActorSystem.VINCLUB) {
            throw new BusinessLogicException(AppErrorCode.INVALID_TRANSACTION_ACTOR_SYSTEM);
        }

        // Store the old transaction state for historical event
        PartnerPointTransactionHistory oldTxn = SerializationUtils.clone(txn);

        // Update the transaction
        txn.setStatus(PointHistoryStatus.SUCCESS);
        txn.setPartnerTransactionId(partnerTransactionId);
        txn.setProcessedTime(processedTime);
        if (Objects.isNull(txn.getMetadata())) {
            txn.setMetadata(JsonNodeFactory.instance.objectNode());
        }

        if (StringUtils.hasText(request)) {
            txn.getMetadata().set(MetadataKey.PartnerTransaction.REQUEST, jsonUtils.readTree(request));
        }
        if (StringUtils.hasText(response)) {
            txn.getMetadata().set(MetadataKey.PartnerTransaction.RESPONSE, jsonUtils.readTree(response));
        }

        // Save the updated transaction
        PartnerPointTransactionHistory savedTxn = partnerPointTransactionHistoryRepository.save(txn);

        // Trigger historical event
        logHistoricalService.logHistoricalEvent(oldTxn, savedTxn, savedTxn.getUpdatedTime(), HistoricalAction.UPDATED);

        return savedTxn;
    }

    @Override
    @Transactional
    @Profiler
    public PartnerPointTransactionHistory markTxnFailed(String transactionId, String failedReason) {
        return markTxnFailed(transactionId, failedReason, null, null);
    }

    @Transactional
    @Profiler
    @Override
    public PartnerPointTransactionHistory markTxnFailed(String transactionId, String failedReason, String request, String response) {
        PartnerPointTransactionHistory txn = findAndValidateTransaction(transactionId);

        if (txn.getStatus() != PointHistoryStatus.PROCESSING) {
            throw new BusinessLogicException(AppErrorCode.INVALID_TRANSACTION_STATUS);
        }

        // Store the old transaction state for historical event
        PartnerPointTransactionHistory oldTxn = SerializationUtils.clone(txn);

        // Update the transaction
        txn.setStatus(PointHistoryStatus.FAILED);
        txn.setProcessedTime(Instant.now().toEpochMilli());
        if (Objects.isNull(txn.getMetadata())) {
            txn.setMetadata(JsonNodeFactory.instance.objectNode());
        }
        if (StringUtils.hasText(failedReason)) {
            txn.getMetadata().put(MetadataKey.PartnerTransaction.FAILED_REASON, failedReason);
        }

        if (StringUtils.hasText(request)) {
            txn.getMetadata().set(MetadataKey.PartnerTransaction.REQUEST, jsonUtils.readTree(request));
        }
        if (StringUtils.hasText(response)) {
            txn.getMetadata().set(MetadataKey.PartnerTransaction.RESPONSE, jsonUtils.readTree(response));
        }

        // Save the updated transaction
        PartnerPointTransactionHistory savedTxn = partnerPointTransactionHistoryRepository.save(txn);

        // Trigger historical event
        logHistoricalService.logHistoricalEvent(oldTxn, savedTxn, savedTxn.getUpdatedTime(), HistoricalAction.UPDATED);

        return savedTxn;
    }

    @Override
    @Profiler
    @Transactional
    public void logRequest(String transactionId, ObjectNode request) {
        PartnerPointTransactionHistory txn = findAndValidateTransaction(transactionId);
        if (Objects.nonNull(txn.getMetadata()) && txn.getMetadata().get(MetadataKey.PartnerTransaction.REQUEST) != null) {
            return;
        }
        // Store the old transaction state for historical event
        PartnerPointTransactionHistory oldTxn = SerializationUtils.clone(txn);

        // Update the transaction
        if (Objects.isNull(txn.getMetadata())) {
            txn.setMetadata(JsonNodeFactory.instance.objectNode());
        }
        txn.getMetadata().set(MetadataKey.PartnerTransaction.REQUEST, request);

        // Save the updated transaction
        PartnerPointTransactionHistory savedTxn = partnerPointTransactionHistoryRepository.save(txn);

        // Trigger historical event
        logHistoricalService.logHistoricalEvent(oldTxn, savedTxn, savedTxn.getUpdatedTime(), HistoricalAction.UPDATED);
    }

    @Override
    @Profiler
    @Transactional
    public void logResponse(String transactionId, ObjectNode response) {
        PartnerPointTransactionHistory txn = findAndValidateTransaction(transactionId);
        if (Objects.nonNull(txn.getMetadata()) && txn.getMetadata().get(MetadataKey.PartnerTransaction.RESPONSE) != null) {
            return;
        }
        // Store the old transaction state for historical event
        PartnerPointTransactionHistory oldTxn = SerializationUtils.clone(txn);

        // Update the transaction
        if (Objects.isNull(txn.getMetadata())) {
            txn.setMetadata(JsonNodeFactory.instance.objectNode());
        }
        txn.getMetadata().set(MetadataKey.PartnerTransaction.RESPONSE, response);

        // Save the updated transaction
        PartnerPointTransactionHistory savedTxn = partnerPointTransactionHistoryRepository.save(txn);

        // Trigger historical event
        logHistoricalService.logHistoricalEvent(oldTxn, savedTxn, savedTxn.getUpdatedTime(), HistoricalAction.UPDATED);
    }

    @Override
    @Transactional
    @Profiler
    public PartnerPointTransactionHistory cancelTxn(String transactionId) {
        PartnerPointTransactionHistory txn = findAndValidateTransaction(transactionId);

        if (txn.getStatus() != PointHistoryStatus.PROCESSING) {
            throw new BusinessLogicException(AppErrorCode.INVALID_TRANSACTION_STATUS);
        }

        // Store the old transaction state for historical event
        PartnerPointTransactionHistory oldTxn = SerializationUtils.clone(txn);

        // Update the transaction
        txn.setStatus(PointHistoryStatus.CANCELLED);
        txn.setProcessedTime(Instant.now().toEpochMilli());

        // Save the updated transaction
        PartnerPointTransactionHistory savedTxn = partnerPointTransactionHistoryRepository.save(txn);

        // Trigger historical event
        logHistoricalService.logHistoricalEvent(oldTxn, savedTxn, savedTxn.getUpdatedTime(), HistoricalAction.UPDATED);

        return savedTxn;
    }

    @Override
    @Transactional
    @Profiler
    public PartnerPointTransactionHistory rollbackTxnFromPartner(String transactionId, String internalRefCode, String reason) {
        PartnerPointTransactionHistory txn = findAndValidateTransaction(transactionId);

        if (txn.getStatus() == PointHistoryStatus.ROLLBACK) {
            throw new BusinessLogicException(AppErrorCode.POINT_TXN_ALREADY_ROLLED_BACK);
        }

        if (txn.getStatus() != PointHistoryStatus.SUCCESS) {
            throw new BusinessLogicException(AppErrorCode.INVALID_TRANSACTION_STATUS);
        }

        // Store the old transaction state for historical event
        PartnerPointTransactionHistory oldTxn = SerializationUtils.clone(txn);

        // Update the transaction
        txn.setStatus(PointHistoryStatus.ROLLBACK);
        if (Objects.isNull(txn.getMetadata())) {
            txn.setMetadata(JsonNodeFactory.instance.objectNode());
        }
        if (StringUtils.hasText(reason)) {
            txn.getMetadata().put(MetadataKey.PartnerTransaction.ROLLBACK_REASON, reason);
        }
        txn.setInternalRefCode(internalRefCode);

        // Save the updated transaction
        PartnerPointTransactionHistory savedTxn = partnerPointTransactionHistoryRepository.save(txn);

        // Trigger historical event
        logHistoricalService.logHistoricalEvent(oldTxn, savedTxn, savedTxn.getUpdatedTime(), HistoricalAction.UPDATED);

        return savedTxn;
    }

    @Override
    @Profiler
    public PartnerPointTransactionHistory findById(Long id) {
        var txn = optById(id);
        if (txn.isEmpty()) {
            throw new BusinessLogicException(AppErrorCode.TRANSACTION_NOT_FOUND);
        }
        return txn.get();
    }


    @Profiler
    @Override
    public Optional<PartnerPointTransactionHistory> optById(Long id) {
        return partnerPointTransactionHistoryRepository.findById(id)
                .filter(PartnerPointTransactionHistory::getActive);
    }

    @Override
    @Profiler
    public PartnerPointTransactionHistory findByTransactionId(String transactionId) {
        var txn = optByTransactionId(transactionId);
        if (txn.isEmpty()) {
            throw new BusinessLogicException(AppErrorCode.TRANSACTION_NOT_FOUND);
        }
        return txn.get();
    }

    @Override
    @Profiler
    public Optional<PartnerPointTransactionHistory> optByTransactionId(String transactionId) {
        PartnerPointTransactionHistory cachedTxn = getCacheByTransactionId().get(transactionId);
        if (cachedTxn != null) {
            try (var p = new vn.vinclub.common.model.Profiler(getClass(), "optByTransactionId.hit")){
                return Optional.of(cachedTxn);
            }
        }
        try (var p = new vn.vinclub.common.model.Profiler(getClass(), "optByTransactionId.miss")) {
            Optional<PartnerPointTransactionHistory> optionalTxn = partnerPointTransactionHistoryRepository.findByTransactionId(transactionId)
                    .filter(PartnerPointTransactionHistory::getActive);
            if (optionalTxn.isEmpty()) {
                return Optional.empty();
            }
            getCacheByTransactionId().put(transactionId, optionalTxn.get(), CACHE_TTL_MINUTES, TimeUnit.MINUTES);
            return optionalTxn;
        }
    }

    @Override
    @Profiler
    public Page<PartnerPointTransactionHistory> filter(PartnerPointTxnFilterDto filter, Pageable pageable) {
        return partnerPointTransactionHistoryRepository.findAll(
                PartnerPointTransactionHistorySpecification.getSpecification(filter), pageable);
    }

    @Override
    @Profiler
    public PartnerPointTransactionHistory findByPartnerIdAndPartnerTransactionId(Long partnerId, String partnerTransactionId) {
        return partnerPointTransactionHistoryRepository.findByPartnerIdAndPartnerTransactionId(partnerId, partnerTransactionId)
                .filter(PartnerPointTransactionHistory::getActive)
                .orElseThrow(() -> new BusinessLogicException(AppErrorCode.TRANSACTION_NOT_FOUND));
    }

    @Override
    @Profiler
    public List<PartnerPointTransactionHistory> findProcessingTransactions(Pageable pageable) {
        return partnerPointTransactionHistoryRepository.findAllByStatus(PointHistoryStatus.PROCESSING, pageable);
    }

    @Override
    @Profiler
    public void invalidateCache(String transactionId) {
        getCacheByTransactionId().remove(transactionId);
    }

    @Profiler
    private PartnerPointTransactionHistory findAndValidateTransaction(String transactionId) {
        return partnerPointTransactionHistoryRepository.findByTransactionId(transactionId)
                .filter(PartnerPointTransactionHistory::getActive)
                .orElseThrow(() -> new BusinessLogicException(AppErrorCode.TRANSACTION_NOT_FOUND));
    }

    @Profiler
    private String generateTransactionId() {
        return pointTxnIdGenerator.nextIdAsString();
    }

    @Profiler
    private RMapCache<String, PartnerPointTransactionHistory> getCacheByTransactionId() {
        return redissonClient.getMapCache(CACHE_BY_TRANSACTION_ID_KEY, new SerializationCodec());
    };
}
