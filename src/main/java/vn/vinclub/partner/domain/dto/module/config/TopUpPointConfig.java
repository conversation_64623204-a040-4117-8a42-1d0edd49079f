package vn.vinclub.partner.domain.dto.module.config;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonInclude;
import lombok.*;

import java.util.Map;
import java.util.Set;

@EqualsAndHashCode(callSuper = true)
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@JsonInclude(JsonInclude.Include.NON_NULL)
@JsonIgnoreProperties(ignoreUnknown = true)
public class TopUpPointConfig extends PointLimitConfig {

    private boolean partnerEnabled;
    private boolean vclubEnabled;

    private Set<String> allowPartnerPointCodes;
}
