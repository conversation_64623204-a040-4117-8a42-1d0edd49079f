package vn.vinclub.partner.domain.dto.module.config;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonInclude;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.Map;
import java.util.Set;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@JsonInclude(JsonInclude.Include.NON_NULL)
@JsonIgnoreProperties(ignoreUnknown = true)
public class PointLimitConfig {

    // partner point limit
    private Map<String, Long> maxPartnerPointPerTransactionByPointCode;
    private Map<String, Long> partnerDailyQuotaByPointCode;
    private Map<String, Long> partnerMonthlyQuotaByPointCode;

    // vclub point limit
    private Long maxVclubPointPerTransaction;
    private Long vclubDailyQuota;
    private Long vclubMonthlyQuota;
}
