package vn.vinclub.partner.domain.dto;

import lombok.Builder;
import lombok.Data;
import vn.vinclub.partner.domain.enums.TransactionType;

import java.math.BigDecimal;
import java.util.Map;

@Builder
@Data
public class UserQuotaDto {
    private Long totalTransaction;
    private BigDecimal totalCashAmount;

    private Map<TransactionType, Long> totalPartnerTransactionByType;
    private Map<TransactionType, BigDecimal> totalPartnerCashAmountByType;
    private Map<TransactionType, Long> totalVinclubTransactionByType;
    private Map<TransactionType, BigDecimal> totalVinclubCashAmountByType;
}
